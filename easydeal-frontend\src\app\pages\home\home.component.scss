// Hero Section
.hero-section {
  position: relative;
  min-height: 100vh;
  overflow: hidden;

  .hero-background {
    position: relative;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);

    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    min-height: 100vh;

    .hero-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(30, 60, 114, 0.8);
      z-index: 1;
    }

    .container {
      position: relative;
      z-index: 2;
    }
  }

  .hero-content {
    color: white;
    padding: 2rem 0;

    .hero-title {
      font-size: 3.5rem;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 1.5rem;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);

      @media (max-width: 768px) {
        font-size: 2.5rem;
      }
    }

    .hero-subtitle {
      font-size: 1.25rem;
      margin-bottom: 2rem;
      opacity: 0.9;
      line-height: 1.6;
    }
  }

  .hero-search-form {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);

    .search-tabs {
      display: flex;
      margin-bottom: 1.5rem;
      border-radius: 10px;
      overflow: hidden;
      background: #f8f9fa;

      .search-tab {
        flex: 1;
        padding: 0.75rem 1rem;
        border: none;
        background: transparent;
        color: #6c757d;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;

        &.active {
          background: var(--bs-primary);
          color: white;
        }

        &:hover:not(.active) {
          background: #e9ecef;
        }
      }
    }

    .search-inputs {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr auto;
      gap: 1rem;
      align-items: end;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }

      .search-input-group {
        position: relative;

        i {
          position: absolute;
          left: 1rem;
          top: 50%;
          transform: translateY(-50%);
          color: #6c757d;
          z-index: 3;
        }

        .form-select {
          padding-left: 3rem;
          border: 2px solid #e9ecef;
          border-radius: 10px;
          height: 50px;
          font-weight: 500;

          &:focus {
            border-color: var(--bs-primary);
            box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
          }
        }
      }

      .search-btn {
        height: 50px;
        padding: 0 2rem;
        border-radius: 10px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        white-space: nowrap;
      }
    }
  }

  .hero-cards {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 0;

    .hero-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 15px;
      padding: 2rem;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      }

      &.hero-card-1 {
        animation: slideInRight 0.6s ease-out 0.2s both;
      }

      &.hero-card-2 {
        animation: slideInRight 0.6s ease-out 0.4s both;
      }

      &.hero-card-3 {
        animation: slideInRight 0.6s ease-out 0.6s both;
      }

      .hero-card-content {
        text-align: center;

        .hero-card-icon {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 1.5rem;
        }

        .hero-card-title {
          font-size: 1.5rem;
          font-weight: 700;
          margin-bottom: 1rem;
          color: #2c3e50;
        }

        .hero-card-description {
          color: #6c757d;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Section Styles
.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 768px) {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
}

.section-subtitle {
  font-size: 1.125rem;
  color: #6c757d;
  margin-bottom: 0;
}

// Featured Properties Section
.featured-properties-section {
  .property-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .property-image {
      position: relative;
      height: 200px;
      overflow: hidden;

      img {
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      &:hover img {
        transform: scale(1.05);
      }

      .property-badges {
        position: absolute;
        top: 1rem;
        right: 1rem;
        z-index: 2;
      }

      .property-rating {
        position: absolute;
        top: 1rem;
        left: 1rem;
        background: rgba(255, 255, 255, 0.9);
        padding: 0.25rem 0.5rem;
        border-radius: 20px;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.875rem;
        font-weight: 600;
      }
    }

    .property-content {
      padding: 1.5rem;

      .property-title {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2c3e50;
        line-height: 1.4;
      }

      .property-location {
        color: #6c757d;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        font-size: 0.875rem;
      }

      .property-details {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
        flex-wrap: wrap;

        .property-detail {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.875rem;
          color: #6c757d;

          i {
            font-size: 1rem;
          }
        }
      }

      .property-price {
        display: flex;
        align-items: baseline;
        gap: 0.5rem;

        .price {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--bs-primary);
        }

        .currency {
          color: #6c757d;
          font-size: 0.875rem;
        }
      }
    }
  }
}

// Cities Section
.cities-section {
  .city-card {
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 200px;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .city-image {
      position: relative;
      height: 100%;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      &:hover img {
        transform: scale(1.1);
      }

      .city-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
        display: flex;
        align-items: end;
        padding: 1.5rem;

        .city-content {
          color: white;
          text-align: center;
          width: 100%;

          .city-name {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
          }

          .city-count {
            font-size: 0.875rem;
            opacity: 0.9;
            margin: 0;
          }
        }
      }
    }
  }
}

// Blog Section
.blog-section {
  .article-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .article-image {
      position: relative;
      height: 200px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      &:hover img {
        transform: scale(1.05);
      }

      .article-category {
        position: absolute;
        top: 1rem;
        right: 1rem;
        z-index: 2;
      }
    }

    .article-content {
      padding: 1.5rem;

      .article-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #2c3e50;
        line-height: 1.4;
      }

      .article-excerpt {
        color: #6c757d;
        margin-bottom: 1.5rem;
        line-height: 1.6;
      }

      .article-meta {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        font-size: 0.875rem;

        .article-author,
        .article-date,
        .article-read-time {
          display: flex;
          align-items: center;
          color: #6c757d;
        }
      }
    }
  }
}

// Newsletter Section
.newsletter-section {
  .newsletter-content {
    .newsletter-title {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .newsletter-subtitle {
      font-size: 1.125rem;
      opacity: 0.9;
      margin: 0;
    }
  }

  .newsletter-form {
    .input-group {
      .form-control {
        border: none;
        border-radius: 10px 0 0 10px;
        padding: 1rem 1.5rem;
        font-size: 1rem;

        &:focus {
          box-shadow: none;
          border-color: transparent;
        }
      }

      .btn {
        border-radius: 0 10px 10px 0;
        padding: 1rem 2rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        white-space: nowrap;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .hero-section {
    .hero-content {
      .hero-title {
        font-size: 2rem;
      }

      .hero-subtitle {
        font-size: 1rem;
      }
    }

    .hero-search-form {
      padding: 1.5rem;

      .search-inputs {
        grid-template-columns: 1fr;
        gap: 1rem;
      }
    }

    .hero-cards {
      gap: 1rem;

      .hero-card {
        padding: 1.5rem;
      }
    }
  }

  .section-title {
    font-size: 1.75rem;
  }

  .newsletter-section {
    .newsletter-content {
      text-align: center;
      margin-bottom: 2rem;

      .newsletter-title {
        font-size: 1.5rem;
      }
    }

    .newsletter-form {
      .input-group {
        flex-direction: column;

        .form-control {
          border-radius: 10px;
          margin-bottom: 1rem;
        }

        .btn {
          border-radius: 10px;
          justify-content: center;
        }
      }
    }
  }
}

// Animation Classes
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Custom Utilities
.bg-light-dark-blue {
  background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
}

.text-dark-blue {
  color: var(--bs-primary) !important;
}

.btn-dark-blue {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  color: white;

  &:hover,
  &:focus,
  &:active {
    background-color: rgba(var(--bs-primary-rgb), 0.9);
    border-color: rgba(var(--bs-primary-rgb), 0.9);
    color: white;
  }
}
