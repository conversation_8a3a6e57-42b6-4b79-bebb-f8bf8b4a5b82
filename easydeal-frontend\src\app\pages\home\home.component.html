<!-- Hero Section -->
<section class="hero-section">
  <div class="hero-background">
    <div class="hero-overlay"></div>
    <div class="container">
      <div class="row align-items-center min-vh-100">
        <!-- Hero Content -->
        <div class="col-lg-6">
          <div class="hero-content">
            <h1 class="hero-title">
              ابحث عن العقار المثالي
              <span class="text-primary">بسهولة وثقة</span>
            </h1>
            <p class="hero-subtitle">
              منصة إيزي ديل تقدم لك أفضل العقارات في مصر مع خدمات متميزة وأسعار تنافسية
            </p>

            <!-- Search Form -->
            <div class="hero-search-form">
              <div class="search-tabs">
                <button class="search-tab active">شراء</button>
                <button class="search-tab">إيجار</button>
                <button class="search-tab">استثمار</button>
              </div>

              <div class="search-inputs">
                <div class="search-input-group">
                  <i class="ki-outline ki-geolocation fs-2"></i>
                  <select class="form-select">
                    <option>اختر المدينة</option>
                    <option>القاهرة الجديدة</option>
                    <option>الشيخ زايد</option>
                    <option>العاصمة الإدارية</option>
                  </select>
                </div>

                <div class="search-input-group">
                  <i class="ki-outline ki-home fs-2"></i>
                  <select class="form-select">
                    <option>نوع العقار</option>
                    <option>شقة</option>
                    <option>فيلا</option>
                    <option>مكتب</option>
                  </select>
                </div>

                <div class="search-input-group">
                  <i class="ki-outline ki-dollar fs-2"></i>
                  <select class="form-select">
                    <option>الميزانية</option>
                    <option>أقل من مليون</option>
                    <option>1-3 مليون</option>
                    <option>أكثر من 3 مليون</option>
                  </select>
                </div>

                <button class="btn btn-primary search-btn" (click)="searchProperties()">
                  <i class="ki-outline ki-magnifier fs-2"></i>
                  ابحث
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Hero Cards -->
        <div class="col-lg-6">
          <div class="hero-cards">
            <div class="hero-card" *ngFor="let card of heroCards; let i = index" [class]="'hero-card-' + (i + 1)">
              <div class="hero-card-content">
                <div class="hero-card-icon" [class]="'bg-light-' + card.color">
                  <i [class]="card.icon + ' fs-1 text-' + card.color"></i>
                </div>
                <h3 class="hero-card-title">{{ card.title }}</h3>
                <p class="hero-card-description">{{ card.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Featured Properties Section -->
<section class="featured-properties-section py-15">
  <div class="container">
    <!-- Section Header -->
    <div class="row mb-10">
      <div class="col-12 text-center">
        <h2 class="section-title">
          <i class="ki-outline ki-star fs-1 text-warning me-3"></i>
          العقارات المميزة
        </h2>
        <p class="section-subtitle">اكتشف أفضل العقارات المتاحة حالياً</p>
      </div>
    </div>

    <!-- Properties Grid -->
    <div class="row g-6">
      <div class="col-lg-3 col-md-6" *ngFor="let property of featuredProperties">
        <div class="property-card" (click)="viewProperty(property.id)">
          <!-- Property Image -->
          <div class="property-image">
            <img [src]="property.image" [alt]="property.title" class="w-100">
            <div class="property-badges">
              <span class="badge badge-success" *ngIf="property.isNew">جديد</span>
            </div>
            <div class="property-rating">
              <i class="ki-solid ki-star text-warning"></i>
              <span>{{ property.rating }}</span>
            </div>
          </div>

          <!-- Property Content -->
          <div class="property-content">
            <h4 class="property-title">{{ property.title }}</h4>
            <p class="property-location">
              <i class="ki-outline ki-geolocation text-muted me-2"></i>
              {{ property.location }}
            </p>

            <div class="property-details">
              <div class="property-detail" *ngIf="property.bedrooms > 0">
                <i class="ki-outline ki-home text-muted"></i>
                <span>{{ property.bedrooms }} غرف</span>
              </div>
              <div class="property-detail" *ngIf="property.bathrooms > 0">
                <i class="ki-outline ki-droplet text-muted"></i>
                <span>{{ property.bathrooms }} حمام</span>
              </div>
              <div class="property-detail">
                <i class="ki-outline ki-resize text-muted"></i>
                <span>{{ property.area }} م²</span>
              </div>
            </div>

            <div class="property-price">
              <span class="price">{{ property.price }}</span>
              <span class="currency">جنيه</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- View All Button -->
    <div class="row mt-10">
      <div class="col-12 text-center">
        <button class="btn btn-outline-primary btn-lg">
          عرض جميع العقارات
          <i class="ki-outline ki-arrow-left ms-2"></i>
        </button>
      </div>
    </div>
  </div>
</section>

<!-- Cities Carousel Section -->
<section class="cities-section py-15 bg-light">
  <div class="container">
    <!-- Section Header -->
    <div class="row mb-10">
      <div class="col-12 text-center">
        <h2 class="section-title">
          <i class="ki-outline ki-map fs-1 text-primary me-3"></i>
          استكشف المدن
        </h2>
        <p class="section-subtitle">اختر المدينة التي تناسبك</p>
      </div>
    </div>

    <!-- Cities Carousel -->
    <div class="cities-carousel">
      <div class="row g-4">
        <div class="col-lg-2 col-md-4 col-6" *ngFor="let city of cities">
          <div class="city-card" (click)="viewCity(city.name)">
            <div class="city-image">
              <img [src]="city.image" [alt]="city.name" class="w-100">
              <div class="city-overlay">
                <div class="city-content">
                  <h4 class="city-name">{{ city.name }}</h4>
                  <p class="city-count">{{ city.propertiesCount }} عقار</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Blog Articles Section -->
<section class="blog-section py-15">
  <div class="container">
    <!-- Section Header -->
    <div class="row mb-10">
      <div class="col-12 text-center">
        <h2 class="section-title">
          <i class="ki-outline ki-book fs-1 text-info me-3"></i>
          مقالات تهمك
        </h2>
        <p class="section-subtitle">اقرأ أحدث المقالات والنصائح العقارية</p>
      </div>
    </div>

    <!-- Articles Grid -->
    <div class="row g-6">
      <div class="col-lg-4 col-md-6" *ngFor="let article of blogArticles">
        <div class="article-card" (click)="readArticle(article.id)">
          <!-- Article Image -->
          <div class="article-image">
            <img [src]="article.image" [alt]="article.title" class="w-100">
            <div class="article-category">
              <span class="badge badge-primary">{{ article.category }}</span>
            </div>
          </div>

          <!-- Article Content -->
          <div class="article-content">
            <h4 class="article-title">{{ article.title }}</h4>
            <p class="article-excerpt">{{ article.excerpt }}</p>

            <div class="article-meta">
              <div class="article-author">
                <i class="ki-outline ki-profile-user text-muted me-2"></i>
                <span>{{ article.author }}</span>
              </div>
              <div class="article-date">
                <i class="ki-outline ki-calendar text-muted me-2"></i>
                <span>{{ article.date }}</span>
              </div>
              <div class="article-read-time">
                <i class="ki-outline ki-time text-muted me-2"></i>
                <span>{{ article.readTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- View All Articles Button -->
    <div class="row mt-10">
      <div class="col-12 text-center">
        <button class="btn btn-outline-info btn-lg">
          عرض جميع المقالات
          <i class="ki-outline ki-arrow-left ms-2"></i>
        </button>
      </div>
    </div>
  </div>
</section>

<!-- Newsletter Section -->
<section class="newsletter-section py-15 bg-primary">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-6">
        <div class="newsletter-content text-white">
          <h3 class="newsletter-title">اشترك في النشرة الإخبارية</h3>
          <p class="newsletter-subtitle">
            احصل على أحدث العروض والمقالات العقارية مباشرة في بريدك الإلكتروني
          </p>
        </div>
      </div>
      <div class="col-lg-6">
        <div class="newsletter-form">
          <div class="input-group">
            <input type="email" class="form-control form-control-lg" placeholder="أدخل بريدك الإلكتروني">
            <button class="btn btn-light btn-lg" type="button">
              <i class="ki-outline ki-send fs-2"></i>
              اشترك
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
