import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit {

  // Hero section data
  heroCards = [
    {
      title: 'سهولة',
      description: 'سهولة في التعامل والوصول للعقارات',
      icon: 'ki-outline ki-check-circle',
      color: 'success'
    },
    {
      title: 'سرعة',
      description: 'سرعة في إنجاز المعاملات',
      icon: 'ki-outline ki-flash',
      color: 'warning'
    },
    {
      title: 'ثقة',
      description: 'ثقة في التعامل مع أفضل المطورين',
      icon: 'ki-outline ki-shield-tick',
      color: 'primary'
    }
  ];

  // Featured properties
  featuredProperties = [
    {
      id: 1,
      title: 'شقة للبيع في التجمع الخامس',
      location: 'التجمع الخامس، القاهرة الجديدة',
      price: '2,500,000',
      area: '120',
      bedrooms: 3,
      bathrooms: 2,
      image: 'assets/media/properties/property-1.jpg',
      rating: 4.5,
      isNew: true
    },
    {
      id: 2,
      title: 'فيلا للبيع في الشيخ زايد',
      location: 'الشيخ زايد، الجيزة',
      price: '8,500,000',
      area: '350',
      bedrooms: 5,
      bathrooms: 4,
      image: 'assets/media/properties/property-2.jpg',
      rating: 4.8,
      isNew: false
    },
    {
      id: 3,
      title: 'شقة للإيجار في المعادي',
      location: 'المعادي، القاهرة',
      price: '15,000',
      area: '90',
      bedrooms: 2,
      bathrooms: 1,
      image: 'assets/media/properties/property-3.jpg',
      rating: 4.2,
      isNew: true
    },
    {
      id: 4,
      title: 'مكتب للبيع في وسط البلد',
      location: 'وسط البلد، القاهرة',
      price: '1,200,000',
      area: '80',
      bedrooms: 0,
      bathrooms: 1,
      image: 'assets/media/properties/property-4.jpg',
      rating: 4.0,
      isNew: false
    }
  ];

  // Cities carousel
  cities = [
    {
      name: 'القاهرة الجديدة',
      image: 'assets/media/cities/new-cairo.jpg',
      propertiesCount: 1250
    },
    {
      name: 'الشيخ زايد',
      image: 'assets/media/cities/sheikh-zayed.jpg',
      propertiesCount: 890
    },
    {
      name: 'العاصمة الإدارية',
      image: 'assets/media/cities/new-capital.jpg',
      propertiesCount: 650
    },
    {
      name: 'الساحل الشمالي',
      image: 'assets/media/cities/north-coast.jpg',
      propertiesCount: 420
    },
    {
      name: 'العين السخنة',
      image: 'assets/media/cities/ain-sokhna.jpg',
      propertiesCount: 320
    }
  ];

  // Blog articles
  blogArticles = [
    {
      id: 1,
      title: 'نصائح لشراء العقار المناسب',
      excerpt: 'دليل شامل لاختيار العقار المناسب لاحتياجاتك وميزانيتك',
      image: 'assets/media/blog/article-1.jpg',
      author: 'أحمد محمد',
      date: '2024-01-15',
      readTime: '5 دقائق',
      category: 'نصائح عقارية'
    },
    {
      id: 2,
      title: 'استثمار العقارات في مصر',
      excerpt: 'كيفية الاستثمار الناجح في العقارات والحصول على عائد مجزي',
      image: 'assets/media/blog/article-2.jpg',
      author: 'سارة أحمد',
      date: '2024-01-10',
      readTime: '7 دقائق',
      category: 'استثمار'
    },
    {
      id: 3,
      title: 'أحدث المشاريع العقارية',
      excerpt: 'تعرف على أحدث المشاريع العقارية في القاهرة الجديدة والعاصمة الإدارية',
      image: 'assets/media/blog/article-3.jpg',
      author: 'محمد علي',
      date: '2024-01-05',
      readTime: '4 دقائق',
      category: 'مشاريع جديدة'
    }
  ];

  constructor() { }

  ngOnInit(): void {
    // Initialize any required data or services
  }

  // Navigation methods
  searchProperties(): void {
    // Navigate to properties search page
    console.log('Searching properties...');
  }

  viewProperty(propertyId: number): void {
    // Navigate to property details
    console.log('Viewing property:', propertyId);
  }

  viewCity(cityName: string): void {
    // Navigate to city properties
    console.log('Viewing city:', cityName);
  }

  readArticle(articleId: number): void {
    // Navigate to blog article
    console.log('Reading article:', articleId);
  }
}
